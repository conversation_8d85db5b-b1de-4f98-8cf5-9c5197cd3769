import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params;
    const {
      url,
      title,
      scrapingSelectors,
      serverKey,
      newServerKey
    } = await request.json();

    // Get Redis client
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    const { key: categoryKey, isMultiLevel } = scrapingSelectors;

    // If moving to a new server
    if (newServerKey && newServerKey !== serverKey) {
      // Add to new server
      const newServerLinks = JSON.parse(await redisClient.hget('scrapingLinks', newServerKey) || '[]');
      newServerLinks.push({
        scrapingSelectors: {
          key: categoryKey.toLowerCase().replace(/\s/g, ''),
          isMultiLevel: Bo<PERSON>an(isMultiLevel),
          querySelector: '_75nlfW',
        },
        category: categoryKey.toLowerCase().replace(/\s/g, ''),
        key,
        url,
        title,
      });
      await redisClient.hset('scrapingLinks', newServerKey, JSON.stringify(newServerLinks));

      // Remove from old server
      const oldServerLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');
      const filteredLinks = oldServerLinks.filter((link: any) => link.key !== key);
      await redisClient.hset('scrapingLinks', serverKey, JSON.stringify(filteredLinks));

      return NextResponse.json({ message: 'Link moved successfully' });
    }

    // Update existing link
    const scrapingLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');
    const linkIndex = scrapingLinks.findIndex((link: any) => link.key === key);

    if (linkIndex === -1) {
      return NextResponse.json({ error: 'Link not found' }, { status: 404 });
    }

    // Update the link
    scrapingLinks[linkIndex] = {
      scrapingSelectors: {
        key: categoryKey.toLowerCase().replace(/\s/g, ''),
        isMultiLevel: Boolean(isMultiLevel),
        querySelector: '_75nlfW',
      },
      category: categoryKey.toLowerCase().replace(/\s/g, ''),
      key,
      url,
      title,
    };

    await redisClient.hset('scrapingLinks', serverKey, JSON.stringify(scrapingLinks));

    return NextResponse.json({ message: 'Link updated successfully' });

  } catch (error) {
    console.error('Error updating link:', error);
    return NextResponse.json(
      { error: 'Failed to update link' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params;
    const { searchParams } = new URL(request.url);
    const serverKey = searchParams.get('key');

    if (!serverKey) {
      return NextResponse.json({ error: 'Server key is required' }, { status: 400 });
    }

    // Get Redis client
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    const scrapingLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');
    const linkIndex = scrapingLinks.findIndex((link: any) => link.key === key);

    if (linkIndex === -1) {
      return NextResponse.json({ error: 'Link not found' }, { status: 404 });
    }

    // Soft delete - mark as deleted instead of removing
    scrapingLinks[linkIndex].deleted = true;
    scrapingLinks[linkIndex].deletedAt = new Date().toISOString();
    scrapingLinks[linkIndex].updatedAt = new Date().toISOString();

    await redisClient.hset('scrapingLinks', serverKey, JSON.stringify(scrapingLinks));

    // Notify backend to restart (but don't sync deletes to file)
    try {
      const publishMessage = {
        action: 'delete',
        serverKey,
        linkKey: key,
        timestamp: new Date().toISOString()
      };
      console.log('Publishing link delete message:', publishMessage);

      const result = await redisClient.publish('link_changes', JSON.stringify(publishMessage));
      console.log(`Published to ${result} subscribers`);
    } catch (publishError) {
      console.error('Failed to publish link change:', publishError);
    }

    return NextResponse.json({ message: 'Link deleted successfully (soft delete)' });

  } catch (error) {
    console.error('Error deleting link:', error);
    return NextResponse.json(
      { error: 'Failed to delete link' },
      { status: 500 }
    );
  }
}

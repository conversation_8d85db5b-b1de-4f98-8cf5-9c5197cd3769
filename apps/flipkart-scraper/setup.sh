#!/bin/bash

echo "🚀 Setting up Flipkart Scraper Backend..."

# Check if Redis is installed and running
echo "📡 Checking Redis..."
if command -v redis-server &> /dev/null; then
    echo "✅ Redis is installed"

    # Check if Redis is running
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis is running"
    else
        echo "⚠️  Redis is not running. Starting Redis..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            brew services start redis 2>/dev/null || redis-server --daemonize yes
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            sudo systemctl start redis-server 2>/dev/null || redis-server --daemonize yes
        else
            echo "Please start Redis manually: redis-server"
        fi
    fi
else
    echo "❌ Redis is not installed. Please install Redis:"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "   macOS: brew install redis"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "   Ubuntu/Debian: sudo apt-get install redis-server"
        echo "   CentOS/RHEL: sudo yum install redis"
    fi
    echo "   Or visit: https://redis.io/download"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
if command -v pnpm &> /dev/null; then
    pnpm install
elif command -v npm &> /dev/null; then
    npm install
else
    echo "❌ Neither pnpm nor npm found. Please install Node.js and npm first."
    exit 1
fi

echo "✅ Setup complete!"
echo ""
echo "🎯 Next steps:"
echo "   1. Make sure Redis is running: redis-cli ping"
echo "   2. Start the application: npx nx serve flipkart-scraper"
echo "   3. Or use: pnpm start"
echo ""
echo "🔧 Useful commands:"
echo "   - Check Redis: npx nx check-redis flipkart-scraper"
echo "   - View logs: tail -f logs/*.log"






2024-07-27 18:23:42 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:24:11 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:24:25 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:24:29 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:24:57 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:25:06 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:25:17 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:25:36 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:25:56 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:26:05 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:26:13 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2024-07-27 18:26:26 [31merror[39m: [31mUnhandled Rejection: expected value at line 1 column 1[39m
2025-06-18 02:05:26 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 02:05:26 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 02:05:26 [36mdebug[39m: [36mSeeding Redis data done.[39m
2025-06-18 02:05:37 [32minfo[39m: [32m[39m

[32m Sending message to telegram ACCH7Z9KDFQUG2FH[39m

[32m[39m
2025-06-18 02:05:37 [32minfo[39m: [32mSending message to allowed chat ids[39m
2025-06-18 02:07:11 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 02:07:11 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 02:07:11 [36mdebug[39m: [36mSeeding Redis data done.[39m
2025-06-18 02:09:37 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 02:09:37 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 02:09:37 [36mdebug[39m: [36mSeeding Redis data done.[39m
2025-06-18 02:38:39 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 02:38:39 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 02:38:39 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 02:38:39 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 02:38:39 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 03:35:33 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 03:35:33 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 03:35:33 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 03:35:33 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 03:35:33 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 03:40:56 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:41:01 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 03:41:01 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 03:41:01 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 03:41:01 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 03:41:01 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 03:52:52 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 03:52:52 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 03:52:52 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 03:52:52 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 03:52:52 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 03:52:52 [32minfo[39m: [32mStarting scraping for 4 active links[39m
2025-06-18 03:52:52 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 03:52:52 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 03:56:04 [32minfo[39m: [32mReceived link change: add for server server-1 at 2025-06-17T22:26:04.837Z[39m
2025-06-18 03:56:04 [32minfo[39m: [32mProcessing link addition: dslr (dslr-fruu6)[39m
2025-06-18 03:56:04 [32minfo[39m: [32mSuccessfully synced links to file after addition[39m
2025-06-18 03:56:04 [32minfo[39m: [32mTriggering application restart: Link added[39m
2025-06-18 03:56:06 [32minfo[39m: [32mRestarting application due to link changes...[39m
2025-06-18 03:56:06 [32minfo[39m: [32mLink sync service closed[39m
2025-06-18 03:56:07 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 03:56:07 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 03:56:07 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 03:56:07 [32minfo[39m: [32mStarting scraping for 4 active links[39m
2025-06-18 03:56:07 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 03:56:08 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 03:58:10 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 04:06:41 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 04:06:41 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 04:06:41 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 04:06:41 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 04:06:41 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 04:06:41 [32minfo[39m: [32mStarting scraping for 4 active links[39m
2025-06-18 04:06:41 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 04:06:41 [32minfo[39m: [32mLinkSyncService: Connected to Redis[39m
2025-06-18 04:06:41 [32minfo[39m: [32mLinkSyncService: Redis connection ready[39m
2025-06-18 04:06:41 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 04:07:46 [32minfo[39m: [32mLinkSyncService: Received message on channel link_changes: {"action":"add","serverKey":"server-1","link":{"scrapingSelectors":{"key":"laptops","isMultiLevel":true,"querySelector":"_75nlfW"},"category":"laptops","key":"laptops-2ikkq","url":"https://www.flipkart.com/laptops/pr?sid=6bo%2Cb5g","title":"Test Laptops","deleted":false,"createdAt":"2025-06-17T22:37:46.305Z","updatedAt":"2025-06-17T22:37:46.305Z"},"timestamp":"2025-06-17T22:37:46.306Z"}[39m
2025-06-18 04:07:46 [32minfo[39m: [32mReceived link change: add for server server-1 at 2025-06-17T22:37:46.306Z[39m
2025-06-18 04:07:46 [32minfo[39m: [32mProcessing link addition: Test Laptops (laptops-2ikkq)[39m
2025-06-18 04:07:46 [32minfo[39m: [32mSuccessfully synced links to file after addition[39m
2025-06-18 04:07:46 [32minfo[39m: [32mTriggering application restart: Link added[39m
2025-06-18 04:07:48 [32minfo[39m: [32mRestarting application due to link changes...[39m
2025-06-18 04:07:48 [32minfo[39m: [32mLink sync service closed[39m
2025-06-18 04:07:49 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 04:07:49 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 04:07:49 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 04:07:49 [32minfo[39m: [32mStarting scraping for 4 active links[39m
2025-06-18 04:07:49 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 04:07:49 [32minfo[39m: [32mLinkSyncService: Connected to Redis[39m
2025-06-18 04:07:49 [32minfo[39m: [32mLinkSyncService: Redis connection ready[39m
2025-06-18 04:07:49 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 04:08:45 [32minfo[39m: [32mLinkSyncService: Received message on channel link_changes: {"action":"add","serverKey":"server-1","link":{"title":"Test Link","key":"test-key","category":"test","url":"https://example.com"},"timestamp":"2025-06-17T22:38:45.535Z"}[39m
2025-06-18 04:08:45 [32minfo[39m: [32mReceived link change: add for server server-1 at 2025-06-17T22:38:45.535Z[39m
2025-06-18 04:08:45 [32minfo[39m: [32mProcessing link addition: Test Link (test-key)[39m
2025-06-18 04:08:45 [32minfo[39m: [32mSuccessfully synced links to file after addition[39m
2025-06-18 04:08:45 [32minfo[39m: [32mTriggering application restart: Link added[39m
2025-06-18 04:08:47 [32minfo[39m: [32mRestarting application due to link changes...[39m
2025-06-18 04:08:47 [32minfo[39m: [32mLink sync service closed[39m
2025-06-18 04:08:48 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 04:08:48 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 04:08:48 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 04:08:48 [32minfo[39m: [32mStarting scraping for 4 active links[39m
2025-06-18 04:08:48 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 04:08:48 [32minfo[39m: [32mLinkSyncService: Connected to Redis[39m
2025-06-18 04:08:48 [32minfo[39m: [32mLinkSyncService: Redis connection ready[39m
2025-06-18 04:08:48 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 04:09:52 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 04:09:52 [32minfo[39m: [32mLink sync service closed[39m
2025-06-18 04:10:15 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 04:10:15 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 04:10:15 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 04:10:15 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 04:10:15 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 04:10:15 [32minfo[39m: [32mStarting scraping for 4 active links[39m
2025-06-18 04:10:15 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 04:10:15 [32minfo[39m: [32mLinkSyncService: Connected to Redis[39m
2025-06-18 04:10:15 [32minfo[39m: [32mLinkSyncService: Redis connection ready[39m
2025-06-18 04:10:15 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 04:10:31 [32minfo[39m: [32mLinkSyncService: Received message on channel link_changes: {"action":"add","serverKey":"server-1","link":{"scrapingSelectors":{"key":"dslr","isMultiLevel":false,"querySelector":"_75nlfW"},"category":"dslr","key":"dslr-pasw8","url":"https://www.flipkart.com/all/pr?p%5B%5D=facets.category%255B%255D%3Dall%2Fjek%2Fp31&p%5B%5D=facets.category%255B%255D%3Dall%2Fjek%2Fp31%2Ftrv%2F&p%5B%5D=facets.brand%255B%255D%3DNIKON&p%5B%5D=facets.brand%255B%255D%3DCanon&p%5B%5D=facets.brand%255B%255D%3DOLYMPUS&p%5B%5D=facets.brand%255B%255D%3DSONY&p%5B%5D=facets.brand%255B%255D%3DFUJIFILM&p%5B%5D=facets.brand%255B%255D%3DPanasonic&p%5B%5D=facets.brand%255B%255D%3DPentax&p%5B%5D=facets.discount_range_v1%255B%255D%3D30%2525%2Bor%2Bmore&sort=price_asc&sid=all&pageUID=1728901908166","title":"dslr","deleted":false,"createdAt":"2025-06-17T22:40:31.883Z","updatedAt":"2025-06-17T22:40:31.883Z"},"timestamp":"2025-06-17T22:40:31.883Z"}[39m
2025-06-18 04:10:31 [32minfo[39m: [32mReceived link change: add for server server-1 at 2025-06-17T22:40:31.883Z[39m
2025-06-18 04:10:31 [32minfo[39m: [32mProcessing link addition: dslr (dslr-pasw8)[39m
2025-06-18 04:10:31 [32minfo[39m: [32mSuccessfully synced links to file after addition[39m
2025-06-18 04:10:31 [32minfo[39m: [32mTriggering application restart: Link added[39m
2025-06-18 04:10:33 [32minfo[39m: [32mRestarting application due to link changes...[39m
2025-06-18 04:10:33 [32minfo[39m: [32mLink sync service closed[39m
2025-06-18 04:10:34 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 04:10:34 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 04:10:34 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 04:10:34 [32minfo[39m: [32mStarting scraping for 4 active links[39m
2025-06-18 04:10:34 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 04:10:34 [32minfo[39m: [32mLinkSyncService: Connected to Redis[39m
2025-06-18 04:10:34 [32minfo[39m: [32mLinkSyncService: Redis connection ready[39m
2025-06-18 04:10:34 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 04:11:21 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 04:11:25 [32minfo[39m: [32mCreating new Redis client[39m
2025-06-18 04:11:25 [32minfo[39m: [32mStarting Flipkart Scraper application[39m
2025-06-18 04:11:25 [32minfo[39m: [32mClient connected to redis and ready to use...[39m
2025-06-18 04:11:25 [32minfo[39m: [32mRedis data seeded successfully[39m
2025-06-18 04:11:25 [32minfo[39m: [32mLink sync service initialized[39m
2025-06-18 04:11:25 [32minfo[39m: [32mStarting scraping for 5 active links[39m
2025-06-18 04:11:25 [32minfo[39m: [32mScraping processes initiated for all links[39m
2025-06-18 04:11:25 [32minfo[39m: [32mLinkSyncService: Connected to Redis[39m
2025-06-18 04:11:25 [32minfo[39m: [32mLinkSyncService: Redis connection ready[39m
2025-06-18 04:11:25 [32minfo[39m: [32mSubscribed to 1 Redis channels for link sync[39m
2025-06-18 04:12:38 [32minfo[39m: [32mReceived SIGINT, shutting down gracefully[39m
2025-06-18 04:12:38 [32minfo[39m: [32mLink sync service closed[39m

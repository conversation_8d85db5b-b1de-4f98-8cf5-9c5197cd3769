"use strict";

import { ScrapingService } from "./services/ScrapingService.js";
import { LinkSyncService } from "./services/LinkSyncService.js";
import logger from "./utils/logger.js";
import { ScrapingLink } from "./types/index.js";
import { config } from "./config/index.js";

// Function to check Redis connection
async function checkRedisConnection(): Promise<boolean> {
  try {
    const RedisClient = (await import("./repositories/RedisClient.js")).default;
    const client = RedisClient.getClient();
    await client.ping();
    logger.info("Redis connection verified successfully");
    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Redis connection failed: ${errorMessage}`);
    return false;
  }
}

// Function to seed Redis data
async function seedRedisData(): Promise<void> {
  const scrapingService = new ScrapingService();

  try {
    await Promise.all([
      scrapingService.setAdminIds(),
      scrapingService.setBlockedWords(),
      scrapingService.setAllowedChatIds(),
      scrapingService.setScrapingLinks(),
    ]);

    logger.info("Redis data seeded successfully");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Failed to seed Redis data: ${errorMessage}`);
    throw error;
  }
}

// Global variables for restart functionality
let linkSyncService: LinkSyncService | null = null;
let isRestarting = false;

// Function to restart the application
function restartApplication(): void {
  if (isRestarting) {
    logger.info("Restart already in progress, ignoring");
    return;
  }

  isRestarting = true;
  logger.info("Restarting application due to link changes...");

  // Clean up resources
  if (linkSyncService) {
    linkSyncService.close().catch(err => {
      logger.error("Error closing link sync service:", err);
    });
  }

  // Restart the main function
  setTimeout(() => {
    isRestarting = false;
    main().catch(error => {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error("Error restarting application:", errorMessage);
      process.exit(1);
    });
  }, 1000);
}

// Function to scrape data
async function main(): Promise<void> {
  try {
    if (isRestarting) {
      logger.info("Application is restarting, skipping main execution");
      return;
    }

    logger.info("Starting Flipkart Scraper application");

    // Check Redis connection first
    const isRedisConnected = await checkRedisConnection();
    if (!isRedisConnected) {
      logger.error("Redis is not available. Please ensure Redis is running and accessible.");
      logger.info("To start Redis locally, run: redis-server");
      logger.info("Or check your Redis configuration in the .env file");
      process.exit(1);
    }

    // Seed Redis data
    await seedRedisData();

    // Initialize link sync service for auto-restart functionality
    const redisOptions = {
      host: config.redisHost || 'localhost',
      port: parseInt(config.redisPort || '6379'),
      password: config.redisPassword,
    };

    linkSyncService = new LinkSyncService(redisOptions, restartApplication);
    logger.info("Link sync service initialized");

    const scrapingService = new ScrapingService();
    const links = await scrapingService.getLinksFromRedis();

    if (!links) {
      throw new Error("Failed to get links from Redis");
    }

    logger.info(`Starting scraping for ${links.length} active links`);

    // Start scraping for each link
    for (const link of links) {
      try {
        console.log(`Starting scraping for link`, link.key);
        scrapingService.scrapeLink(link);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`Error scraping data from ${link.url}: ${errorMessage}`);
      }
    }
    
    logger.info("Scraping processes initiated for all links");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error({
      mainErrorMessage: errorMessage,
      error,
    });
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully');

  if (linkSyncService) {
    await linkSyncService.close();
  }

  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully');

  if (linkSyncService) {
    await linkSyncService.close();
  }

  process.exit(0);
});

// Start the application
main().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});

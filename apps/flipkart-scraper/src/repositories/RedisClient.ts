import Redis, { RedisOptions } from "ioredis";
import logger from "../utils/logger.js";
import { RedisClientOptions } from "../types/index.js";

interface ExtendedRedisClientOptions extends RedisOptions {
  port?: number;
  host?: string;
}

class RedisClient {
  private client!: Redis;
  private static instance: RedisClient;

  constructor(options: ExtendedRedisClientOptions) {
    if (!RedisClient.instance) {
      // Add better default options for connection resilience
      const redisOptions: ExtendedRedisClientOptions = {
        ...options,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        connectTimeout: 10000,
        commandTimeout: 5000,
      };

      this.client = new Redis(redisOptions);

      this.client.on("ready", this.onReady);
      this.client.on("error", this.onError);
      this.client.on("end", this.onEnd);
      this.client.on("connect", () => {
        logger.info("Redis client connected successfully");
      });

      RedisClient.instance = this;
    } else {
      logger.info("Redis client already exists");
    }

    return RedisClient.instance;
  }

  // Connection events
  onReady() {
    logger.info("Client connected to redis and ready to use...");
  }

  onError(err: Error) {
    logger.error(`Redis connection error: ${err.message}`);
  }

  onEnd() {
    logger.info("Client disconnected from redis");
  }

  onSigint() {
    this.client.quit();
  }

  getClient(): Redis {
    return this.client;
  }

  async isConnected(): Promise<boolean> {
    try {
      await this.client.ping();
      return true;
    } catch (error) {
      return false;
    }
  }

  async connect(): Promise<void> {
    try {
      await this.client.connect();
      logger.info("Redis client connected manually");
    } catch (error) {
      logger.error(`Failed to connect to Redis: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  // Key-value operations
  async getValue(key: string): Promise<string | null> {
    return this.client.get(key);
  }

  async setValue(key: string, value: string): Promise<"OK"> {
    return this.client.set(key, value);
  }

  async incrementValue(key: string): Promise<number> {
    return this.client.incr(key);
  }

  async expireKey(key: string, seconds: number): Promise<number> {
    return this.client.expire(key, seconds);
  }

  async setHashKey(key: string, field: string, value: string): Promise<number> {
    return this.client.hset(key, field, value);
  }

  async getHashKey(key: string, field: string): Promise<string | null> {
    return this.client.hget(key, field);
  }

  async deleteKey(key: string): Promise<number> {
    return this.client.del(key);
  }

  async keyExists(key: string): Promise<number> {
    return this.client.exists(key);
  }

  async getJSONKey(key: string, subKey: string): Promise<any> {
    return this.client.call("JSON.GET", key, subKey);
  }

  async setJSONKey(key: string, subKey: string, value: any): Promise<any> {
    if (!(await this.keyExists(key)) || !subKey) {
      return this.client.call("JSON.SET", key, "$", value);
    }
    return this.client.call("JSON.SET", key, subKey, value);
  }

  async deleteJSONKey(key: string, subKey: string): Promise<any> {
    return this.client.call("JSON.DEL", key, subKey);
  }

  async pushJSONKey(key: string, path: string, value: any): Promise<any> {
    return this.client.call("JSON.ARRAPPEND", key, path, value);
  }

  async updateJSONKey(key: string, path: string, index: number, value: any): Promise<any> {
    return this.client.call("JSON.ARRINSERT", key, path, index, value);
  }

  async removeJSONKey(key: string, path: string, index: number): Promise<any> {
    return this.client.call("JSON.ARRINDEX", key, path, index);
  }

  async getJSONLength(key: string, path: string): Promise<any> {
    return this.client.call("JSON.ARRLEN", key, path);
  }

  static getInstance(options: ExtendedRedisClientOptions = { port: 6379, host: "127.0.0.1" }): Redis {
    if (!RedisClient.instance) {
      logger.info("Creating new Redis client");
      new RedisClient(options);
    } else {
      logger.info("Redis client already exists");
    }
    return RedisClient.instance.getClient();
  }
}

export default RedisClient.getInstance();

import Redis from "ioredis";
import { config } from "./config/index.js";

// Test script to verify Redis pub/sub functionality
async function testRedisPubSub() {
  console.log('Testing Redis pub/sub functionality...');
  console.log('Config:', {
    host: config.redisHost,
    port: config.redisPort,
    serverKey: config.serverKey
  });

  // Create subscriber
  const subscriber = new Redis({
    host: config.redisHost || 'localhost',
    port: parseInt(config.redisPort || '6379'),
    password: config.redisPassword,
  });

  // Create publisher
  const publisher = new Redis({
    host: config.redisHost || 'localhost',
    port: parseInt(config.redisPort || '6379'),
    password: config.redisPassword,
  });

  // Set up subscriber
  subscriber.on('connect', () => {
    console.log('Subscriber connected to Redis');
  });

  subscriber.on('ready', () => {
    console.log('Subscriber ready');
  });

  subscriber.on('error', (err) => {
    console.error('Subscriber error:', err);
  });

  // Subscribe to link_changes channel
  subscriber.subscribe('link_changes', (err, count) => {
    if (err) {
      console.error('Failed to subscribe:', err);
      return;
    }
    console.log(`Subscribed to ${count} channels`);
  });

  // Handle messages
  subscriber.on('message', (channel, message) => {
    console.log(`Received message on ${channel}:`, message);
    try {
      const parsed = JSON.parse(message);
      console.log('Parsed message:', parsed);
    } catch (e) {
      console.error('Failed to parse message:', e);
    }
  });

  // Wait a bit for subscription to be ready
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Test publishing
  console.log('Publishing test message...');
  const testMessage = {
    action: 'add',
    serverKey: config.serverKey,
    link: {
      title: 'Test Link',
      key: 'test-key',
      category: 'test',
      url: 'https://example.com'
    },
    timestamp: new Date().toISOString()
  };

  const result = await publisher.publish('link_changes', JSON.stringify(testMessage));
  console.log(`Published to ${result} subscribers`);

  // Wait for message to be received
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Cleanup
  await subscriber.quit();
  await publisher.quit();
  console.log('Test completed');
}

// Run the test
testRedisPubSub().catch(console.error);
